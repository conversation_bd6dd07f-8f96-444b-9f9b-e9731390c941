#!/usr/bin/env python3
"""
Simple memory efficiency test to validate the core concept.
"""

import duckdb
import json
import tempfile
import os

def test_simple_memory_comparison():
    """Simple test comparing memory usage between our extension and DuckDB default."""
    
    # Create a simple test with large unused data
    data = []
    for i in range(100):
        obj = {
            "target": f"value_{i}",  # This is what we'll select
            "unused": "x" * 1000,    # 1KB of unused data per object
        }
        data.append(obj)
    
    # Write to temp file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(data, f)
        temp_file = f.name
    
    try:
        print(f"Test file size: {os.path.getsize(temp_file) / 1024:.2f} KB")
        
        # Test our extension
        conn_our = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn_our.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')
        
        result_our = conn_our.execute(f'SELECT target FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"Our extension result count: {len(result_our)}")
        print(f"First result: {result_our[0][0]}")
        
        # Test DuckDB default
        conn_default = duckdb.connect()
        result_default = conn_default.execute(f"SELECT target FROM read_json_auto('{temp_file}')").fetchall()
        print(f"Default reader result count: {len(result_default)}")
        print(f"First result: {result_default[0][0]}")
        
        # Verify results are the same
        assert result_our == result_default
        print("✅ Results match between our extension and default reader")
        
        conn_our.close()
        conn_default.close()
        
    finally:
        os.unlink(temp_file)

if __name__ == "__main__":
    test_simple_memory_comparison()
