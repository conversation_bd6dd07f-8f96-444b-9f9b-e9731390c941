#!/usr/bin/env python3

import duckdb
import json
import tempfile
import os

def test_simple_array():
    """Test simple 1D array to see if the issue affects all arrays."""
    print("Testing simple 1D array...")
    
    # Create simple test data - just a 1D array of numbers
    data = [1, 2, 3]
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(data, f)
        temp_file = f.name
    
    try:
        print(f"Created test file: {temp_file}")
        print(f"Test data: {data}")
        
        # Connect to DuckDB and load extension
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD 'build/debug/streaming_json_reader.duckdb_extension'")
        print("Extension loaded successfully")
        
        # Query the data
        print("Attempting to query simple array...")
        result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"Query succeeded! Result: {result}")
        
        # Check if the data is correct
        print("\nValidating results...")
        assert len(result) == 3, f"Expected 3 rows, got {len(result)}"
        
        # Check each row
        for i, row in enumerate(result):
            expected_value = float(data[i])
            actual_value = row[0]
            print(f"Row {i}: expected {expected_value}, got {actual_value}")
            assert actual_value == expected_value, f"Row {i}: expected {expected_value}, got {actual_value}"
        
        print("All validations passed! Simple array works correctly.")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"Error occurred: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        os.unlink(temp_file)

if __name__ == "__main__":
    success = test_simple_array()
    if success:
        print("\nTest PASSED: Simple array works correctly!")
    else:
        print("\nTest FAILED: Simple array has issues.")
