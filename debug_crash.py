#!/usr/bin/env python3

import duckdb
import json
import tempfile
import os

def test_array_of_structs_crash():
    """Minimal test to reproduce the array of structs crash."""
    print("Testing array of structs crash...")
    
    # Create test data that matches the failing test
    data = {
        "users": [
            {"id": 1, "name": "<PERSON>", "active": True},
            {"id": 2, "name": "<PERSON>", "active": False},
            {"id": 3, "name": "<PERSON>", "active": True}
        ]
    }
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(data, f)
        temp_file = f.name
    
    try:
        print(f"Created test file: {temp_file}")
        print(f"Test data: {data}")
        
        # Connect to DuckDB and load extension
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD 'build/debug/streaming_json_reader.duckdb_extension'")
        print("Extension loaded successfully")
        
        # Try to query the data - this should crash
        print("Attempting to query array of structs...")
        result = conn.execute(f'SELECT users FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"Query succeeded! Result: {result}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error occurred: {e}")
        import traceback
        traceback.print_exc()
    finally:
        os.unlink(temp_file)

if __name__ == "__main__":
    test_array_of_structs_crash()
