#!/usr/bin/env python3
"""
Memory efficiency test using the existing large test file.
Tests projection pushdown and selective data reading.
"""

import duckdb
import os
import time
import psutil

def measure_query_performance(conn, query, description):
    """Measure query performance and memory usage."""
    process = psutil.Process()
    initial_memory = process.memory_info().rss
    
    start_time = time.time()
    result = conn.execute(query).fetchall()
    end_time = time.time()
    
    peak_memory = process.memory_info().rss
    memory_used = peak_memory - initial_memory
    
    return {
        'result': result,
        'execution_time': end_time - start_time,
        'memory_used': memory_used,
        'description': description
    }

def test_large_file_memory_efficiency():
    """Test memory efficiency with the existing large test file."""
    
    large_file = "tests/test_large.json"
    if not os.path.exists(large_file):
        print("Large test file not found, skipping test")
        return
    
    file_size = os.path.getsize(large_file)
    print(f"Large file size: {file_size / 1024 / 1024:.2f} MB")
    
    # Test 1: Select only dataset metadata (should be very efficient)
    print("\n=== Test 1: Select only dataset metadata ===")
    
    # Our extension
    conn_our = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
    conn_our.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')
    
    our_result = measure_query_performance(
        conn_our,
        f'SELECT dataset_info.name, dataset_info.total_users FROM streaming_json_reader("{large_file}")',
        "Our extension - metadata only"
    )
    
    # DuckDB default
    conn_default = duckdb.connect()
    default_result = measure_query_performance(
        conn_default,
        f"SELECT dataset_info.name, dataset_info.total_users FROM read_json_auto('{large_file}')",
        "Default reader - metadata only"
    )
    
    print(f"Our extension: {our_result['execution_time']:.2f}s, {our_result['memory_used'] / 1024 / 1024:.2f} MB")
    print(f"Default reader: {default_result['execution_time']:.2f}s, {default_result['memory_used'] / 1024 / 1024:.2f} MB")
    print(f"Results match: {our_result['result'] == default_result['result']}")
    
    if our_result['memory_used'] > 0 and default_result['memory_used'] > 0:
        efficiency_ratio = default_result['memory_used'] / our_result['memory_used']
        print(f"Memory efficiency ratio: {efficiency_ratio:.2f}x")
    
    # Test 2: Select first user only (should demonstrate projection pushdown)
    print("\n=== Test 2: Select first user only ===")
    
    our_result2 = measure_query_performance(
        conn_our,
        f'SELECT users[1].name, users[1].email FROM streaming_json_reader("{large_file}")',
        "Our extension - first user only"
    )
    
    default_result2 = measure_query_performance(
        conn_default,
        f"SELECT users[1].name, users[1].email FROM read_json_auto('{large_file}')",
        "Default reader - first user only"
    )
    
    print(f"Our extension: {our_result2['execution_time']:.2f}s, {our_result2['memory_used'] / 1024 / 1024:.2f} MB")
    print(f"Default reader: {default_result2['execution_time']:.2f}s, {default_result2['memory_used'] / 1024 / 1024:.2f} MB")
    print(f"Results match: {our_result2['result'] == default_result2['result']}")
    
    if our_result2['memory_used'] > 0 and default_result2['memory_used'] > 0:
        efficiency_ratio2 = default_result2['memory_used'] / our_result2['memory_used']
        print(f"Memory efficiency ratio: {efficiency_ratio2:.2f}x")
    
    # Test 3: Count users (should be very efficient for our extension)
    print("\n=== Test 3: Count users ===")
    
    our_result3 = measure_query_performance(
        conn_our,
        f'SELECT COUNT(*) FROM (SELECT unnest(users) FROM streaming_json_reader("{large_file}"))',
        "Our extension - count users"
    )
    
    default_result3 = measure_query_performance(
        conn_default,
        f"SELECT COUNT(*) FROM (SELECT unnest(users) FROM read_json_auto('{large_file}'))",
        "Default reader - count users"
    )
    
    print(f"Our extension: {our_result3['execution_time']:.2f}s, {our_result3['memory_used'] / 1024 / 1024:.2f} MB")
    print(f"Default reader: {default_result3['execution_time']:.2f}s, {default_result3['memory_used'] / 1024 / 1024:.2f} MB")
    print(f"Results match: {our_result3['result'] == default_result3['result']}")
    
    if our_result3['memory_used'] > 0 and default_result3['memory_used'] > 0:
        efficiency_ratio3 = default_result3['memory_used'] / our_result3['memory_used']
        print(f"Memory efficiency ratio: {efficiency_ratio3:.2f}x")
    
    # Summary
    print(f"\n=== Summary ===")
    print(f"File size: {file_size / 1024 / 1024:.2f} MB")
    print(f"All results match: {our_result['result'] == default_result['result'] and our_result2['result'] == default_result2['result'] and our_result3['result'] == default_result3['result']}")
    
    conn_our.close()
    conn_default.close()

if __name__ == "__main__":
    test_large_file_memory_efficiency()
