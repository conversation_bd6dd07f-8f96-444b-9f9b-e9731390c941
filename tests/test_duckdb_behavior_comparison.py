#!/usr/bin/env python3
"""
Test script to compare our JSON extension behavior with DuckDB's default JSON reader.
This helps ensure compatibility and understand differences in behavior.
"""

import duckdb
import json
import tempfile
import os
import pytest

class TestDuckDBBehaviorComparison:
    """Compare our extension behavior with DuckDB's built-in JSON reader."""
    
    @pytest.fixture
    def duckdb_conn(self):
        """Create a DuckDB connection."""
        return duckdb.connect()
    
    @pytest.fixture
    def temp_json_file(self):
        """Create a temporary JSON file for testing."""
        fd, path = tempfile.mkstemp(suffix='.json')
        os.close(fd)
        yield path
        if os.path.exists(path):
            os.unlink(path)
    
    def create_json_file(self, data, filepath):
        """Helper to create JSON test files."""
        with open(filepath, 'w') as f:
            json.dump(data, f)

    def test_mixed_nesting_comparison(self, duckdb_conn, temp_json_file):
        """Compare how DuckDB's default reader handles mixed nesting vs our extension."""
        # Mixed nesting data that our extension rejects
        mixed_data = [[1, [2, 3], [[4, 5]]]]
        self.create_json_file(mixed_data, temp_json_file)
        
        # Test DuckDB's default behavior
        try:
            default_result = duckdb_conn.execute(f"SELECT * FROM read_json_auto('{temp_json_file}')").fetchall()
            print(f"DuckDB default result: {default_result}")
            
            # DuckDB should handle this gracefully by converting to JSON strings
            assert len(default_result) == 3  # Should have 3 rows
            
        except Exception as e:
            pytest.fail(f"DuckDB default reader failed unexpectedly: {e}")
        
        # Test our extension behavior (should fail)
        duckdb_conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')
        
        with pytest.raises(duckdb.InvalidInputException) as exc_info:
            duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        
        # Verify our extension correctly rejects inconsistent schema
        error_message = str(exc_info.value).lower()
        assert any(keyword in error_message for keyword in [
            'type', 'schema', 'conflict', 'merge', 'inconsistent', 'parsing'
        ])

    def test_simple_array_comparison(self, duckdb_conn, temp_json_file):
        """Compare simple array handling between default and our extension."""
        simple_data = [[1, 2, 3], [4, 5, 6]]
        self.create_json_file(simple_data, temp_json_file)
        
        # Test DuckDB's default behavior
        default_result = duckdb_conn.execute(f"SELECT * FROM read_json_auto('{temp_json_file}')").fetchall()
        print(f"DuckDB default result: {default_result}")
        
        # Test our extension
        duckdb_conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')
        our_result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        print(f"Our extension result: {our_result}")
        
        # Both should succeed, though format may differ
        assert len(default_result) == 2
        assert len(our_result) == 2

    def test_object_array_comparison(self, duckdb_conn, temp_json_file):
        """Compare object with array fields handling."""
        object_data = [
            {"numbers": [1, 2, 3], "name": "test1"},
            {"numbers": [4, 5], "name": "test2"}
        ]
        self.create_json_file(object_data, temp_json_file)
        
        # Test DuckDB's default behavior
        default_result = duckdb_conn.execute(f"SELECT * FROM read_json_auto('{temp_json_file}')").fetchall()
        print(f"DuckDB default result: {default_result}")
        
        # Test our extension
        duckdb_conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')
        our_result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        print(f"Our extension result: {our_result}")
        
        # Both should succeed
        assert len(default_result) == 2
        assert len(our_result) == 2

    @pytest.mark.skip(reason="For manual testing - prints schema information")
    def test_schema_inference_comparison(self, duckdb_conn, temp_json_file):
        """Compare schema inference between default and our extension."""
        test_data = [
            {"name": "Alice", "scores": [85, 92, 78], "active": True},
            {"name": "Bob", "scores": [90, 88], "active": False}
        ]
        self.create_json_file(test_data, temp_json_file)
        
        # Check DuckDB's inferred schema
        try:
            schema_result = duckdb_conn.execute(f"DESCRIBE SELECT * FROM read_json_auto('{temp_json_file}')").fetchall()
            print(f"DuckDB inferred schema: {schema_result}")
        except Exception as e:
            print(f"DuckDB schema inference failed: {e}")
        
        # Check our extension's behavior
        try:
            duckdb_conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')
            our_schema = duckdb_conn.execute(f"DESCRIBE SELECT * FROM streaming_json_reader('{temp_json_file}')").fetchall()
            print(f"Our extension schema: {our_schema}")
        except Exception as e:
            print(f"Our extension schema inference failed: {e}")
