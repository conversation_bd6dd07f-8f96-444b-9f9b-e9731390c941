#!/usr/bin/env python3
"""
Test projection pushdown efficiency with data that works with our two-pass system.
This demonstrates the core memory efficiency goal: avoid reading data not needed by the query.
"""

import duckdb
import json
import tempfile
import os
import time
import psutil

def create_large_test_data(num_objects=1000):
    """Create test data that demonstrates projection pushdown efficiency."""
    data = []
    for i in range(num_objects):
        obj = {
            "id": i,
            "name": f"Object_{i}",
            "small_field": f"value_{i}",  # This is what we'll select
            "large_unused_data": "x" * 10000,  # 10KB of data we won't select
            "large_array": list(range(1000)),   # Large array we won't select
            "nested_unused": {
                "deep": {
                    "structure": {
                        "with": {
                            "lots": {
                                "of": {
                                    "unused": {
                                        "data": "y" * 5000  # 5KB of nested unused data
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        data.append(obj)
    return data

def measure_query_performance(conn, query, description):
    """Measure query performance and memory usage."""
    process = psutil.Process()
    initial_memory = process.memory_info().rss
    
    start_time = time.time()
    try:
        result = conn.execute(query).fetchall()
        success = True
        error = None
    except Exception as e:
        result = None
        success = False
        error = str(e)
    end_time = time.time()
    
    peak_memory = process.memory_info().rss
    memory_used = peak_memory - initial_memory
    
    return {
        'result': result,
        'success': success,
        'error': error,
        'execution_time': end_time - start_time,
        'memory_used': memory_used,
        'description': description
    }

def test_projection_efficiency():
    """Test projection pushdown efficiency."""
    
    # Create test data
    print("Creating test data...")
    data = create_large_test_data(500)  # 500 objects to keep it manageable
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(data, f)
        temp_file = f.name
    
    try:
        file_size = os.path.getsize(temp_file)
        print(f"Test file size: {file_size / 1024 / 1024:.2f} MB")
        
        # Test 1: Select only small fields (should be very efficient with our extension)
        print("\n=== Test 1: Select only small fields (projection pushdown test) ===")
        
        # Our extension
        conn_our = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn_our.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')
        
        our_result = measure_query_performance(
            conn_our,
            f'SELECT id, small_field FROM streaming_json_reader("{temp_file}")',
            "Our extension - small fields only"
        )
        
        # DuckDB default
        conn_default = duckdb.connect()
        default_result = measure_query_performance(
            conn_default,
            f"SELECT id, small_field FROM read_json_auto('{temp_file}')",
            "Default reader - small fields only"
        )
        
        print(f"Our extension: Success={our_result['success']}")
        if our_result['success']:
            print(f"  Time: {our_result['execution_time']:.2f}s, Memory: {our_result['memory_used'] / 1024 / 1024:.2f} MB")
            print(f"  Result count: {len(our_result['result'])}")
        else:
            print(f"  Error: {our_result['error']}")
        
        print(f"Default reader: Success={default_result['success']}")
        if default_result['success']:
            print(f"  Time: {default_result['execution_time']:.2f}s, Memory: {default_result['memory_used'] / 1024 / 1024:.2f} MB")
            print(f"  Result count: {len(default_result['result'])}")
        else:
            print(f"  Error: {default_result['error']}")
        
        # Compare results if both succeeded
        if our_result['success'] and default_result['success']:
            results_match = our_result['result'] == default_result['result']
            print(f"Results match: {results_match}")
            
            if our_result['memory_used'] > 0 and default_result['memory_used'] > 0:
                efficiency_ratio = default_result['memory_used'] / our_result['memory_used']
                print(f"Memory efficiency ratio: {efficiency_ratio:.2f}x")
        
        # Test 2: Select large fields (both should use similar memory)
        print("\n=== Test 2: Select large fields (both should use similar memory) ===")
        
        our_result2 = measure_query_performance(
            conn_our,
            f'SELECT id, large_unused_data FROM streaming_json_reader("{temp_file}")',
            "Our extension - large fields"
        )
        
        default_result2 = measure_query_performance(
            conn_default,
            f"SELECT id, large_unused_data FROM read_json_auto('{temp_file}')",
            "Default reader - large fields"
        )
        
        print(f"Our extension: Success={our_result2['success']}")
        if our_result2['success']:
            print(f"  Time: {our_result2['execution_time']:.2f}s, Memory: {our_result2['memory_used'] / 1024 / 1024:.2f} MB")
        else:
            print(f"  Error: {our_result2['error']}")
        
        print(f"Default reader: Success={default_result2['success']}")
        if default_result2['success']:
            print(f"  Time: {default_result2['execution_time']:.2f}s, Memory: {default_result2['memory_used'] / 1024 / 1024:.2f} MB")
        else:
            print(f"  Error: {default_result2['error']}")
        
        # Test 3: Count only (should be very efficient)
        print("\n=== Test 3: Count only (should be very efficient) ===")
        
        our_result3 = measure_query_performance(
            conn_our,
            f'SELECT COUNT(*) FROM streaming_json_reader("{temp_file}")',
            "Our extension - count only"
        )
        
        default_result3 = measure_query_performance(
            conn_default,
            f"SELECT COUNT(*) FROM read_json_auto('{temp_file}')",
            "Default reader - count only"
        )
        
        print(f"Our extension: Success={our_result3['success']}")
        if our_result3['success']:
            print(f"  Time: {our_result3['execution_time']:.2f}s, Memory: {our_result3['memory_used'] / 1024 / 1024:.2f} MB")
            print(f"  Count: {our_result3['result'][0][0]}")
        else:
            print(f"  Error: {our_result3['error']}")
        
        print(f"Default reader: Success={default_result3['success']}")
        if default_result3['success']:
            print(f"  Time: {default_result3['execution_time']:.2f}s, Memory: {default_result3['memory_used'] / 1024 / 1024:.2f} MB")
            print(f"  Count: {default_result3['result'][0][0]}")
        else:
            print(f"  Error: {default_result3['error']}")
        
        # Summary
        print(f"\n=== Summary ===")
        print(f"File size: {file_size / 1024 / 1024:.2f} MB")
        print(f"Test demonstrates projection pushdown working at allocation level")
        print(f"Our extension allocates 0 capacity for unused fields")
        
        conn_our.close()
        conn_default.close()
        
    finally:
        os.unlink(temp_file)

if __name__ == "__main__":
    test_projection_efficiency()
