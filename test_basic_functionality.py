#!/usr/bin/env python3
"""
Test basic functionality to ensure our changes didn't break anything.
"""

import duckdb
import json
import tempfile
import os

def test_basic_functionality():
    """Test that basic JSON reading still works."""
    
    # Create simple test data
    data = [
        {"name": "<PERSON>", "age": 30},
        {"name": "<PERSON>", "age": 25},
        {"name": "<PERSON>", "age": 35}
    ]
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(data, f)
        temp_file = f.name
    
    try:
        print(f"Test file size: {os.path.getsize(temp_file)} bytes")
        
        # Test our extension
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')
        
        print("\n=== Testing basic functionality ===")
        
        # Test 1: Select all fields
        try:
            result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
            print(f"✅ SELECT * succeeded! Result count: {len(result)}")
            print(f"First result: {result[0]}")
        except Exception as e:
            print(f"❌ SELECT * failed: {e}")
        
        # Test 2: Select specific field
        try:
            result = conn.execute(f'SELECT name FROM streaming_json_reader("{temp_file}")').fetchall()
            print(f"✅ SELECT name succeeded! Result count: {len(result)}")
            print(f"First result: {result[0]}")
        except Exception as e:
            print(f"❌ SELECT name failed: {e}")
        
        # Test 3: Count
        try:
            result = conn.execute(f'SELECT COUNT(*) FROM streaming_json_reader("{temp_file}")').fetchall()
            print(f"✅ COUNT succeeded! Count: {result[0][0]}")
        except Exception as e:
            print(f"❌ COUNT failed: {e}")
        
        conn.close()
        
    finally:
        os.unlink(temp_file)

if __name__ == "__main__":
    test_basic_functionality()
