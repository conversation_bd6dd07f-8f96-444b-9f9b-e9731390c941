#!/usr/bin/env python3

import duckdb
import json
import tempfile
import os

def test_2d_array_offset_fix():
    """Test that the offset fix resolves the data duplication issue."""
    print("Testing 2D array offset fix...")
    
    # Create test data that should expose the offset issue
    data = [[[1, 2], [3, 4]], [[5, 6], [7, 8]]]
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(data, f)
        temp_file = f.name
    
    try:
        print(f"Created test file: {temp_file}")
        print(f"Test data: {data}")
        
        # Connect to DuckDB and load extension
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD 'build/debug/streaming_json_reader.duckdb_extension'")
        print("Extension loaded successfully")
        
        # Query the data
        print("Attempting to query 2D array...")
        result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"Query succeeded! Result: {result}")
        
        # Check if the data is correct
        print("\nValidating results...")
        assert len(result) == 2, f"Expected 2 rows, got {len(result)}"
        
        # First row should contain [[1, 2], [3, 4]]
        first_row = result[0][0]
        print(f"First row: {first_row}")
        assert len(first_row) == 2, f"Expected 2 sub-arrays in first row, got {len(first_row)}"
        assert first_row[0] == [1.0, 2.0], f"Expected [1.0, 2.0], got {first_row[0]}"
        assert first_row[1] == [3.0, 4.0], f"Expected [3.0, 4.0], got {first_row[1]}"
        
        # Second row should contain [[5, 6], [7, 8]]
        second_row = result[1][0]
        print(f"Second row: {second_row}")
        assert len(second_row) == 2, f"Expected 2 sub-arrays in second row, got {len(second_row)}"
        assert second_row[0] == [5.0, 6.0], f"Expected [5.0, 6.0], got {second_row[0]}"
        assert second_row[1] == [7.0, 8.0], f"Expected [7.0, 8.0], got {second_row[1]}"
        
        print("All validations passed! Offset fix is working correctly.")
        
        conn.close()
        
    except Exception as e:
        print(f"Error occurred: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        os.unlink(temp_file)
    
    return True

if __name__ == "__main__":
    success = test_2d_array_offset_fix()
    if success:
        print("\nTest PASSED: Offset fix is working correctly!")
    else:
        print("\nTest FAILED: Offset issue still exists.")
