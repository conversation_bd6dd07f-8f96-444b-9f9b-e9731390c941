#!/usr/bin/env python3

import duckdb
import json
import tempfile
import os

def test_schema_inference():
    """Test what schema is being inferred for our test data."""
    print("Testing schema inference...")
    
    # Create simple test data
    data = [[[1, 2], [3, 4]], [[5, 6], [7, 8]]]
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(data, f)
        temp_file = f.name
    
    try:
        print(f"Created test file: {temp_file}")
        print(f"Test data: {data}")
        
        # Connect to DuckDB and load extension
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD 'build/debug/streaming_json_reader.duckdb_extension'")
        print("Extension loaded successfully")
        
        # Try to get schema information by looking at the debug output
        print("Attempting to query data to see debug output...")
        try:
            result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
            print(f"Query result: {result}")
        except Exception as e:
            print(f"Query failed with error: {e}")
            # This is expected - we want to see the debug output
        
        conn.close()
        
    except Exception as e:
        print(f"Error occurred: {e}")
        import traceback
        traceback.print_exc()
    finally:
        os.unlink(temp_file)

if __name__ == "__main__":
    test_schema_inference()
